"""
半导体制造机台能力预测引擎
实现RT值预测和数据补全功能
"""

import tensorflow as tf
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
import logging
from itertools import product
import warnings

from data_processor import EquipmentDataProcessor
from training_pipeline import TrainingPipeline

logger = logging.getLogger(__name__)

class RTPredictor:
    """RT预测器"""
    
    def __init__(self, 
                 model: tf.keras.Model,
                 data_processor: EquipmentDataProcessor):
        """
        初始化预测器
        Args:
            model: 训练好的模型
            data_processor: 数据处理器
        """
        self.model = model
        self.data_processor = data_processor
        
    def predict_single(self, 
                      capability: str,
                      recipe: str,
                      equip: str,
                      sub_equip: str,
                      qty: int) -> Dict[str, float]:
        """
        单个预测
        """
        # 创建输入数据
        input_data = pd.DataFrame({
            'capability': [capability],
            'recipe': [recipe],
            'equip': [equip],
            'sub_equip': [sub_equip],
            'qty': [qty],
            'rt': [np.nan]  # 待预测的值
        })
        
        # 数据预处理
        X = self.data_processor.transform(input_data)
        
        # 预测
        y_pred_scaled = self.model.predict(X, verbose=0)
        y_pred = self.data_processor.inverse_transform_target(y_pred_scaled.flatten())
        
        # 计算预测置信度（基于模型的不确定性）
        confidence = self._calculate_confidence(X, y_pred_scaled)
        
        return {
            'predicted_rt': float(y_pred[0]),
            'confidence': float(confidence),
            'input_features': {
                'capability': capability,
                'recipe': recipe,
                'equip': equip,
                'sub_equip': sub_equip,
                'qty': qty
            }
        }
    
    def predict_batch(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        批量预测
        """
        # 数据预处理
        X = self.data_processor.transform(df)
        
        # 预测
        y_pred_scaled = self.model.predict(X, verbose=0)
        y_pred = self.data_processor.inverse_transform_target(y_pred_scaled.flatten())
        
        # 计算置信度
        confidences = [self._calculate_confidence(X[i:i+1], y_pred_scaled[i:i+1]) 
                      for i in range(len(X))]
        
        # 添加预测结果到DataFrame
        result_df = df.copy()
        result_df['predicted_rt'] = y_pred
        result_df['confidence'] = confidences
        
        return result_df
    
    def _calculate_confidence(self, X: np.ndarray, y_pred: np.ndarray) -> float:
        """
        计算预测置信度
        基于模型的预测方差和特征的相似性
        """
        # 使用Monte Carlo Dropout估计不确定性
        predictions = []
        for _ in range(10):  # 多次预测
            pred = self.model(X, training=True)  # 启用dropout
            predictions.append(pred.numpy())
        
        predictions = np.array(predictions)
        variance = np.var(predictions, axis=0)
        
        # 将方差转换为置信度分数 (0-1)
        confidence = 1.0 / (1.0 + variance.flatten()[0])
        
        return min(max(confidence, 0.0), 1.0)

class DataCompletion:
    """数据补全器"""
    
    def __init__(self, 
                 predictor: RTPredictor,
                 qty_range: Tuple[int, int] = (1, 25)):
        """
        初始化数据补全器
        Args:
            predictor: RT预测器
            qty_range: qty的范围
        """
        self.predictor = predictor
        self.qty_range = qty_range
        
    def complete_missing_combinations(self, 
                                    df: pd.DataFrame,
                                    target_combinations: List[Dict] = None) -> pd.DataFrame:
        """
        补全缺失的组合
        Args:
            df: 原始数据
            target_combinations: 指定要补全的组合，如果为None则自动生成
        """
        if target_combinations is None:
            target_combinations = self._generate_missing_combinations(df)
        
        # 创建补全数据
        completion_data = []
        for combo in target_combinations:
            for qty in range(self.qty_range[0], self.qty_range[1] + 1):
                # 检查是否已存在
                existing = df[
                    (df['capability'] == combo['capability']) &
                    (df['recipe'] == combo['recipe']) &
                    (df['equip'] == combo['equip']) &
                    (df['sub_equip'] == combo['sub_equip']) &
                    (df['qty'] == qty)
                ]
                
                if existing.empty:
                    completion_data.append({
                        'capability': combo['capability'],
                        'recipe': combo['recipe'],
                        'equip': combo['equip'],
                        'sub_equip': combo['sub_equip'],
                        'qty': qty,
                        'rt': np.nan
                    })
        
        if not completion_data:
            logger.info("没有需要补全的数据")
            return df
        
        # 创建补全DataFrame
        completion_df = pd.DataFrame(completion_data)
        
        # 预测缺失的RT值
        predicted_df = self.predictor.predict_batch(completion_df)
        predicted_df['rt'] = predicted_df['predicted_rt']
        predicted_df['is_predicted'] = True
        
        # 合并原始数据和预测数据
        original_df = df.copy()
        original_df['is_predicted'] = False
        
        # 选择相同的列
        common_columns = ['capability', 'recipe', 'equip', 'sub_equip', 'qty', 'rt', 'is_predicted']
        result_df = pd.concat([
            original_df[common_columns],
            predicted_df[common_columns]
        ], ignore_index=True)
        
        logger.info(f"补全了 {len(completion_data)} 条记录")
        
        return result_df
    
    def complete_partial_combinations(self, 
                                    df: pd.DataFrame,
                                    min_coverage: float = 0.8) -> pd.DataFrame:
        """
        补全部分组合（只有部分qty值的组合）
        Args:
            df: 原始数据
            min_coverage: 最小覆盖率，低于此值的组合将被补全
        """
        # 分析每个组合的qty覆盖情况
        combinations = df.groupby(['capability', 'recipe', 'equip', 'sub_equip']).agg({
            'qty': ['min', 'max', 'count', 'nunique']
        }).reset_index()
        
        combinations.columns = ['capability', 'recipe', 'equip', 'sub_equip', 
                              'qty_min', 'qty_max', 'qty_count', 'qty_unique']
        
        # 计算覆盖率
        total_possible = self.qty_range[1] - self.qty_range[0] + 1
        combinations['coverage'] = combinations['qty_unique'] / total_possible
        
        # 找出需要补全的组合
        incomplete_combinations = combinations[combinations['coverage'] < min_coverage]
        
        logger.info(f"发现 {len(incomplete_combinations)} 个不完整的组合需要补全")
        
        # 补全数据
        completion_data = []
        for _, combo in incomplete_combinations.iterrows():
            # 获取该组合已有的qty值
            existing_qtys = set(df[
                (df['capability'] == combo['capability']) &
                (df['recipe'] == combo['recipe']) &
                (df['equip'] == combo['equip']) &
                (df['sub_equip'] == combo['sub_equip'])
            ]['qty'].values)
            
            # 补全缺失的qty值
            for qty in range(self.qty_range[0], self.qty_range[1] + 1):
                if qty not in existing_qtys:
                    completion_data.append({
                        'capability': combo['capability'],
                        'recipe': combo['recipe'],
                        'equip': combo['equip'],
                        'sub_equip': combo['sub_equip'],
                        'qty': qty,
                        'rt': np.nan
                    })
        
        if not completion_data:
            logger.info("没有需要补全的部分组合")
            return df
        
        # 预测并合并
        completion_df = pd.DataFrame(completion_data)
        predicted_df = self.predictor.predict_batch(completion_df)
        predicted_df['rt'] = predicted_df['predicted_rt']
        predicted_df['is_predicted'] = True
        
        # 合并数据
        original_df = df.copy()
        original_df['is_predicted'] = False
        
        common_columns = ['capability', 'recipe', 'equip', 'sub_equip', 'qty', 'rt', 'is_predicted']
        result_df = pd.concat([
            original_df[common_columns],
            predicted_df[common_columns]
        ], ignore_index=True)
        
        logger.info(f"补全了 {len(completion_data)} 条部分组合记录")
        
        return result_df
    
    def _generate_missing_combinations(self, df: pd.DataFrame) -> List[Dict]:
        """
        生成缺失的组合
        """
        # 获取所有唯一值
        capabilities = df['capability'].unique()
        recipes = df['recipe'].unique()
        equips = df['equip'].unique()
        sub_equips = df['sub_equip'].unique()
        
        # 生成所有可能的组合
        all_combinations = list(product(capabilities, recipes, equips, sub_equips))
        
        # 获取已存在的组合
        existing_combinations = set(
            df.groupby(['capability', 'recipe', 'equip', 'sub_equip']).size().index
        )
        
        # 找出缺失的组合
        missing_combinations = []
        for combo in all_combinations:
            if combo not in existing_combinations:
                missing_combinations.append({
                    'capability': combo[0],
                    'recipe': combo[1],
                    'equip': combo[2],
                    'sub_equip': combo[3]
                })
        
        logger.info(f"发现 {len(missing_combinations)} 个完全缺失的组合")
        
        return missing_combinations

class SmartPredictor:
    """智能预测器，结合多种策略"""
    
    def __init__(self, 
                 predictor: RTPredictor,
                 data_completion: DataCompletion):
        """
        初始化智能预测器
        """
        self.predictor = predictor
        self.data_completion = data_completion
        
    def predict_with_interpolation(self, 
                                 capability: str,
                                 recipe: str,
                                 equip: str,
                                 sub_equip: str,
                                 qty: int,
                                 df: pd.DataFrame) -> Dict[str, float]:
        """
        结合插值的预测
        """
        # 首先尝试直接预测
        direct_prediction = self.predictor.predict_single(
            capability, recipe, equip, sub_equip, qty
        )
        
        # 查找相似的组合进行插值
        similar_data = df[
            (df['capability'] == capability) &
            (df['recipe'] == recipe) &
            (df['equip'] == equip) &
            (df['sub_equip'] == sub_equip)
        ].copy()
        
        if len(similar_data) >= 2:
            # 使用插值方法
            similar_data = similar_data.sort_values('qty')
            interpolated_rt = np.interp(qty, similar_data['qty'], similar_data['rt'])
            
            # 结合直接预测和插值结果
            combined_prediction = (
                direct_prediction['predicted_rt'] * 0.7 + 
                interpolated_rt * 0.3
            )
            
            return {
                'predicted_rt': float(combined_prediction),
                'direct_prediction': direct_prediction['predicted_rt'],
                'interpolated_rt': float(interpolated_rt),
                'confidence': direct_prediction['confidence'] * 1.1,  # 提高置信度
                'method': 'combined'
            }
        else:
            direct_prediction['method'] = 'direct'
            return direct_prediction
    
    def batch_predict_with_quality_control(self, 
                                         df: pd.DataFrame,
                                         confidence_threshold: float = 0.7) -> pd.DataFrame:
        """
        带质量控制的批量预测
        """
        # 批量预测
        result_df = self.predictor.predict_batch(df)
        
        # 质量控制
        low_confidence = result_df['confidence'] < confidence_threshold
        
        if low_confidence.any():
            logger.warning(f"发现 {low_confidence.sum()} 个低置信度预测")
            
            # 对低置信度的预测进行标记
            result_df['quality_flag'] = np.where(
                low_confidence, 'low_confidence', 'high_confidence'
            )
        else:
            result_df['quality_flag'] = 'high_confidence'
        
        return result_df
