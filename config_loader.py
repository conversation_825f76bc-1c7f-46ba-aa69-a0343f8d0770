"""
配置加载器
用于加载和管理系统配置
"""

import yaml
import os
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class ConfigLoader:
    """配置加载器类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化配置加载器
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        """
        if not os.path.exists(self.config_path):
            logger.warning(f"配置文件 {self.config_path} 不存在，使用默认配置")
            return self._get_default_config()
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"成功加载配置文件: {self.config_path}")
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置
        """
        return {
            'data': {
                'feature_columns': ['capability', 'recipe', 'equip', 'sub_equip', 'qty'],
                'target_column': 'rt',
                'test_size': 0.2,
                'val_size': 0.2,
                'qty_range': {'min': 1, 'max': 25}
            },
            'model': {
                'type': 'standard',
                'architecture': {
                    'hidden_units': [256, 128, 64, 32],
                    'dropout_rate': 0.3,
                    'l2_reg': 0.001
                },
                'compile': {
                    'learning_rate': 0.001
                }
            },
            'training': {
                'epochs': 100,
                'batch_size': 32,
                'verbose': 1,
                'early_stopping': {
                    'patience': 20
                }
            },
            'prediction': {
                'confidence_threshold': 0.7
            },
            'visualization': {
                'figure_size': [12, 8],
                'dpi': 300
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持嵌套键（如 'model.architecture.hidden_units'）
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_data_config(self) -> Dict[str, Any]:
        """获取数据配置"""
        return self.get('data', {})
    
    def get_model_config(self) -> Dict[str, Any]:
        """获取模型配置"""
        return self.get('model', {})
    
    def get_training_config(self) -> Dict[str, Any]:
        """获取训练配置"""
        return self.get('training', {})
    
    def get_prediction_config(self) -> Dict[str, Any]:
        """获取预测配置"""
        return self.get('prediction', {})
    
    def get_visualization_config(self) -> Dict[str, Any]:
        """获取可视化配置"""
        return self.get('visualization', {})
    
    def update_config(self, key: str, value: Any):
        """
        更新配置值
        """
        keys = key.split('.')
        config = self.config
        
        # 导航到最后一级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
        logger.info(f"更新配置: {key} = {value}")
    
    def save_config(self, save_path: Optional[str] = None):
        """
        保存配置到文件
        """
        save_path = save_path or self.config_path
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            logger.info(f"配置已保存到: {save_path}")
        except Exception as e:
            logger.error(f"保存配置失败: {e}")

# 全局配置实例
config = ConfigLoader()

# 便捷函数
def get_config(key: str, default: Any = None) -> Any:
    """获取配置值的便捷函数"""
    return config.get(key, default)

def update_config(key: str, value: Any):
    """更新配置值的便捷函数"""
    config.update_config(key, value)

def get_data_config() -> Dict[str, Any]:
    """获取数据配置的便捷函数"""
    return config.get_data_config()

def get_model_config() -> Dict[str, Any]:
    """获取模型配置的便捷函数"""
    return config.get_model_config()

def get_training_config() -> Dict[str, Any]:
    """获取训练配置的便捷函数"""
    return config.get_training_config()

if __name__ == "__main__":
    # 测试配置加载器
    print("数据配置:", get_data_config())
    print("模型配置:", get_model_config())
    print("训练配置:", get_training_config())
    
    # 测试获取嵌套配置
    print("隐藏层单元:", get_config('model.architecture.hidden_units'))
    print("学习率:", get_config('model.compile.learning_rate'))
    
    # 测试更新配置
    update_config('model.compile.learning_rate', 0.002)
    print("更新后的学习率:", get_config('model.compile.learning_rate'))
