"""
半导体制造机台能力预测模型架构
基于TensorFlow 2实现的深度学习模型
"""

import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers, Model
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class EquipmentRTPredictor:
    """半导体制造机台RT预测模型"""
    
    def __init__(self, 
                 input_dim: int,
                 embedding_dims: Dict[str, int] = None,
                 hidden_units: List[int] = [256, 128, 64, 32],
                 dropout_rate: float = 0.3,
                 l2_reg: float = 0.001):
        """
        初始化模型
        Args:
            input_dim: 输入特征维度
            embedding_dims: 嵌入层维度字典
            hidden_units: 隐藏层单元数列表
            dropout_rate: Dropout比率
            l2_reg: L2正则化系数
        """
        self.input_dim = input_dim
        self.embedding_dims = embedding_dims or {}
        self.hidden_units = hidden_units
        self.dropout_rate = dropout_rate
        self.l2_reg = l2_reg
        self.model = None
        self.history = None
        
    def build_model(self) -> Model:
        """
        构建深度神经网络模型
        考虑半导体制造的特点：
        1. 分类特征的嵌入表示
        2. 数值特征的归一化
        3. 特征交互
        4. 残差连接
        """
        # 输入层
        inputs = keras.Input(shape=(self.input_dim,), name='features')
        
        # 特征提取层
        x = layers.Dense(self.hidden_units[0], 
                        activation='relu',
                        kernel_regularizer=keras.regularizers.l2(self.l2_reg),
                        name='feature_extraction')(inputs)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(self.dropout_rate)(x)
        
        # 深度特征学习层
        for i, units in enumerate(self.hidden_units[1:], 1):
            # 残差连接（如果维度匹配）
            residual = x
            
            x = layers.Dense(units, 
                           activation='relu',
                           kernel_regularizer=keras.regularizers.l2(self.l2_reg),
                           name=f'hidden_{i}')(x)
            x = layers.BatchNormalization()(x)
            x = layers.Dropout(self.dropout_rate)(x)
            
            # 添加残差连接（如果维度匹配）
            if residual.shape[-1] == x.shape[-1]:
                x = layers.Add(name=f'residual_{i}')([x, residual])
        
        # 注意力机制（可选）
        attention = layers.Dense(x.shape[-1], activation='sigmoid', name='attention')(x)
        x = layers.Multiply(name='attention_applied')([x, attention])
        
        # 输出层
        outputs = layers.Dense(1, 
                             activation='linear',
                             name='rt_prediction')(x)
        
        model = Model(inputs=inputs, outputs=outputs, name='equipment_rt_predictor')
        
        return model
    
    def build_ensemble_model(self, num_models: int = 3) -> List[Model]:
        """
        构建集成模型
        """
        models = []
        for i in range(num_models):
            # 为每个模型使用稍微不同的架构
            hidden_units = [int(unit * (0.8 + 0.4 * np.random.random())) for unit in self.hidden_units]
            dropout_rate = self.dropout_rate * (0.8 + 0.4 * np.random.random())
            
            # 临时修改参数
            original_hidden = self.hidden_units
            original_dropout = self.dropout_rate
            
            self.hidden_units = hidden_units
            self.dropout_rate = dropout_rate
            
            model = self.build_model()
            model._name = f'equipment_rt_predictor_{i}'
            models.append(model)
            
            # 恢复原始参数
            self.hidden_units = original_hidden
            self.dropout_rate = original_dropout
            
        return models
    
    def compile_model(self, 
                     model: Model,
                     learning_rate: float = 0.001,
                     loss_weights: Dict[str, float] = None) -> Model:
        """
        编译模型
        """
        # 自定义损失函数，考虑半导体制造的特点
        def custom_loss(y_true, y_pred):
            # 基础MSE损失
            mse_loss = tf.keras.losses.mean_squared_error(y_true, y_pred)
            
            # 添加相对误差惩罚（对于小值更敏感）
            relative_error = tf.abs((y_true - y_pred) / (y_true + 1e-8))
            relative_loss = tf.reduce_mean(relative_error)
            
            # 添加异常值惩罚
            outlier_threshold = 2.0  # 标准差倍数
            outlier_penalty = tf.where(
                tf.abs(y_true - y_pred) > outlier_threshold,
                tf.square(y_true - y_pred) * 2.0,
                0.0
            )
            outlier_loss = tf.reduce_mean(outlier_penalty)
            
            return mse_loss + 0.1 * relative_loss + 0.05 * outlier_loss
        
        # 自定义指标
        def mean_absolute_percentage_error(y_true, y_pred):
            return tf.reduce_mean(tf.abs((y_true - y_pred) / (y_true + 1e-8))) * 100
        
        def r_squared(y_true, y_pred):
            ss_res = tf.reduce_sum(tf.square(y_true - y_pred))
            ss_tot = tf.reduce_sum(tf.square(y_true - tf.reduce_mean(y_true)))
            return 1 - ss_res / (ss_tot + 1e-8)
        
        # 编译模型
        optimizer = keras.optimizers.Adam(learning_rate=learning_rate)
        
        model.compile(
            optimizer=optimizer,
            loss=custom_loss,
            metrics=[
                'mae',
                'mse',
                mean_absolute_percentage_error,
                r_squared
            ]
        )
        
        return model
    
    def create_callbacks(self, 
                        model_save_path: str = 'best_model.h5',
                        patience: int = 20,
                        reduce_lr_patience: int = 10) -> List:
        """
        创建训练回调函数
        """
        callbacks = [
            # 早停
            keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=patience,
                restore_best_weights=True,
                verbose=1
            ),
            
            # 学习率调度
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=reduce_lr_patience,
                min_lr=1e-7,
                verbose=1
            ),
            
            # 模型保存
            keras.callbacks.ModelCheckpoint(
                filepath=model_save_path,
                monitor='val_loss',
                save_best_only=True,
                save_weights_only=False,
                verbose=1
            ),
            
            # TensorBoard日志
            keras.callbacks.TensorBoard(
                log_dir='./logs',
                histogram_freq=1,
                write_graph=True,
                write_images=True
            )
        ]
        
        return callbacks
    
    def build_and_compile(self, **compile_kwargs) -> Model:
        """
        构建并编译模型
        """
        self.model = self.build_model()
        self.model = self.compile_model(self.model, **compile_kwargs)
        
        logger.info("模型构建完成")
        logger.info(f"模型参数数量: {self.model.count_params():,}")
        
        return self.model
    
    def get_model_summary(self) -> str:
        """
        获取模型摘要
        """
        if self.model is None:
            return "模型未构建"
        
        import io
        import sys
        
        # 捕获模型摘要输出
        old_stdout = sys.stdout
        sys.stdout = buffer = io.StringIO()
        
        self.model.summary()
        
        sys.stdout = old_stdout
        summary = buffer.getvalue()
        
        return summary

class AdvancedEquipmentPredictor(EquipmentRTPredictor):
    """高级半导体制造机台预测模型"""
    
    def build_transformer_model(self) -> Model:
        """
        构建基于Transformer的模型
        适用于序列特征和复杂特征交互
        """
        inputs = keras.Input(shape=(self.input_dim,), name='features')
        
        # 将输入重塑为序列格式
        x = layers.Reshape((self.input_dim, 1))(inputs)
        
        # 位置编码
        position_encoding = self._get_position_encoding(self.input_dim, 1)
        x = x + position_encoding
        
        # Multi-Head Attention
        attention_output = layers.MultiHeadAttention(
            num_heads=4,
            key_dim=32
        )(x, x)
        
        # Add & Norm
        x = layers.Add()([x, attention_output])
        x = layers.LayerNormalization()(x)
        
        # Feed Forward
        ff_output = layers.Dense(64, activation='relu')(x)
        ff_output = layers.Dense(1)(ff_output)
        
        # Add & Norm
        x = layers.Add()([x, ff_output])
        x = layers.LayerNormalization()(x)
        
        # Global Average Pooling
        x = layers.GlobalAveragePooling1D()(x)
        
        # 最终预测层
        outputs = layers.Dense(1, activation='linear', name='rt_prediction')(x)
        
        model = Model(inputs=inputs, outputs=outputs, name='transformer_rt_predictor')
        
        return model
    
    def _get_position_encoding(self, seq_len: int, d_model: int) -> tf.Tensor:
        """
        生成位置编码
        """
        position = tf.range(seq_len, dtype=tf.float32)[:, tf.newaxis]
        div_term = tf.exp(tf.range(0, d_model, 2, dtype=tf.float32) * 
                         -(np.log(10000.0) / d_model))
        
        pos_encoding = tf.zeros((seq_len, d_model))
        pos_encoding = tf.concat([
            tf.sin(position * div_term),
            tf.cos(position * div_term)
        ], axis=-1)
        
        return pos_encoding[tf.newaxis, ...]
    
    def build_wide_and_deep_model(self) -> Model:
        """
        构建Wide & Deep模型
        结合线性模型和深度模型的优势
        """
        inputs = keras.Input(shape=(self.input_dim,), name='features')
        
        # Wide部分（线性模型）
        wide = layers.Dense(1, activation='linear', name='wide_part')(inputs)
        
        # Deep部分（深度模型）
        deep = inputs
        for i, units in enumerate(self.hidden_units):
            deep = layers.Dense(units, activation='relu', name=f'deep_{i}')(deep)
            deep = layers.BatchNormalization()(deep)
            deep = layers.Dropout(self.dropout_rate)(deep)
        
        deep = layers.Dense(1, activation='linear', name='deep_part')(deep)
        
        # 组合Wide和Deep
        outputs = layers.Add(name='wide_and_deep')([wide, deep])
        
        model = Model(inputs=inputs, outputs=outputs, name='wide_deep_rt_predictor')
        
        return model
