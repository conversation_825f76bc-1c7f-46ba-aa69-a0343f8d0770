"""
半导体制造机台能力数据处理模块
用于处理capability、recipe、equip、sub_equip、qty、rt等字段的数据
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder, StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
import tensorflow as tf
from typing import Dict, List, Tuple, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EquipmentDataProcessor:
    """半导体制造机台数据处理器"""
    
    def __init__(self):
        self.label_encoders = {}
        self.scalers = {}
        self.feature_columns = ['capability', 'recipe', 'equip', 'sub_equip', 'qty']
        self.target_column = 'rt'
        self.is_fitted = False
        self.stat_features = {}  # 存储统计特征
        self.final_feature_cols = []  # 存储最终使用的特征列
        
    def load_data(self, data_path: Optional[str] = None, data_df: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        加载数据
        Args:
            data_path: 数据文件路径
            data_df: 直接传入的DataFrame
        Returns:
            处理后的DataFrame
        """
        if data_df is not None:
            df = data_df.copy()
        elif data_path:
            if data_path.endswith('.csv'):
                df = pd.read_csv(data_path)
            elif data_path.endswith('.xlsx'):
                df = pd.read_excel(data_path)
            else:
                raise ValueError("支持的文件格式: .csv, .xlsx")
        else:
            raise ValueError("必须提供data_path或data_df")
            
        logger.info(f"加载数据完成，形状: {df.shape}")
        return df
    
    def validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        验证数据完整性和格式
        """
        required_columns = self.feature_columns + [self.target_column]
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise ValueError(f"缺少必要列: {missing_columns}")
            
        # 检查数据类型
        if not pd.api.types.is_numeric_dtype(df['qty']):
            logger.warning("qty列不是数值类型，尝试转换")
            df['qty'] = pd.to_numeric(df['qty'], errors='coerce')
            
        if not pd.api.types.is_numeric_dtype(df['rt']):
            logger.warning("rt列不是数值类型，尝试转换")
            df['rt'] = pd.to_numeric(df['rt'], errors='coerce')
            
        # 检查异常值
        if (df['qty'] <= 0).any():
            logger.warning("发现qty <= 0的记录，将被过滤")
            df = df[df['qty'] > 0]
            
        if (df['rt'] <= 0).any():
            logger.warning("发现rt <= 0的记录，将被过滤")
            df = df[df['rt'] > 0]
            
        logger.info(f"数据验证完成，最终形状: {df.shape}")
        return df
    
    def clean_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        数据清洗
        """
        # 删除重复记录
        initial_shape = df.shape[0]
        df = df.drop_duplicates()
        logger.info(f"删除重复记录: {initial_shape - df.shape[0]} 条")
        
        # 处理缺失值
        missing_info = df.isnull().sum()
        if missing_info.any():
            logger.info(f"缺失值统计:\n{missing_info[missing_info > 0]}")
            
        # 对于分类特征，用众数填充
        for col in ['capability', 'recipe', 'equip', 'sub_equip']:
            if df[col].isnull().any():
                mode_value = df[col].mode()[0] if not df[col].mode().empty else 'unknown'
                df[col] = df[col].fillna(mode_value)
                logger.info(f"{col}列缺失值用'{mode_value}'填充")
        
        # 删除rt为空的记录（这是我们要预测的目标）
        df_with_rt = df.dropna(subset=['rt'])
        df_missing_rt = df[df['rt'].isnull()]
        
        logger.info(f"有rt值的记录: {len(df_with_rt)} 条")
        logger.info(f"缺失rt值的记录: {len(df_missing_rt)} 条")
        
        return df_with_rt, df_missing_rt
    
    def encode_categorical_features(self, df: pd.DataFrame, fit: bool = True) -> pd.DataFrame:
        """
        编码分类特征
        """
        df_encoded = df.copy()
        categorical_cols = ['capability', 'recipe', 'equip', 'sub_equip']
        
        for col in categorical_cols:
            if fit:
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                df_encoded[f'{col}_encoded'] = self.label_encoders[col].fit_transform(df_encoded[col].astype(str))
            else:
                if col in self.label_encoders:
                    # 处理新的类别
                    unique_values = set(df_encoded[col].astype(str).unique())
                    known_values = set(self.label_encoders[col].classes_)
                    new_values = unique_values - known_values
                    
                    if new_values:
                        logger.warning(f"{col}列发现新类别: {new_values}")
                        # 将新类别映射到一个特殊值
                        df_encoded[col] = df_encoded[col].astype(str).replace(list(new_values), 'unknown')
                    
                    df_encoded[f'{col}_encoded'] = self.label_encoders[col].transform(df_encoded[col].astype(str))
                else:
                    raise ValueError(f"标签编码器未训练: {col}")
        
        return df_encoded
    
    def scale_numerical_features(self, df: pd.DataFrame, fit: bool = True) -> pd.DataFrame:
        """
        标准化数值特征
        """
        df_scaled = df.copy()
        numerical_cols = ['qty']
        
        for col in numerical_cols:
            if fit:
                if col not in self.scalers:
                    self.scalers[col] = StandardScaler()
                df_scaled[f'{col}_scaled'] = self.scalers[col].fit_transform(df_scaled[[col]])
            else:
                if col in self.scalers:
                    df_scaled[f'{col}_scaled'] = self.scalers[col].transform(df_scaled[[col]])
                else:
                    raise ValueError(f"标准化器未训练: {col}")
        
        # 对目标变量rt也进行标准化（如果存在）
        if 'rt' in df_scaled.columns and not df_scaled['rt'].isnull().all():
            if fit:
                if 'rt' not in self.scalers:
                    self.scalers['rt'] = StandardScaler()
                df_scaled['rt_scaled'] = self.scalers['rt'].fit_transform(df_scaled[['rt']])
            else:
                if 'rt' in self.scalers:
                    df_scaled['rt_scaled'] = self.scalers['rt'].transform(df_scaled[['rt']])
        
        return df_scaled
    
    def create_features(self, df: pd.DataFrame, fit: bool = True) -> pd.DataFrame:
        """
        特征工程
        """
        df_features = df.copy()

        # 创建组合特征
        df_features['equip_sub_combo'] = df_features['equip'].astype(str) + '_' + df_features['sub_equip'].astype(str)
        df_features['capability_recipe_combo'] = df_features['capability'].astype(str) + '_' + df_features['recipe'].astype(str)

        # 创建qty的分箱特征
        df_features['qty_bin'] = pd.cut(df_features['qty'], bins=5, labels=['very_low', 'low', 'medium', 'high', 'very_high'])

        # 计算每个组合的统计特征（基于已有数据）
        if fit and 'rt' in df_features.columns and not df_features['rt'].isnull().all():
            # 按不同维度计算rt的统计信息并保存
            for group_col in ['equip', 'capability', 'recipe']:
                group_stats = df_features.groupby(group_col)['rt'].agg(['mean', 'std', 'count']).reset_index()
                group_stats.columns = [group_col] + [f'{group_col}_rt_{stat}' for stat in ['mean', 'std', 'count']]

                # 保存统计信息用于预测时使用
                self.stat_features[group_col] = group_stats.set_index(group_col).to_dict('index')

                df_features = df_features.merge(group_stats, on=group_col, how='left')
        elif not fit and self.stat_features:
            # 使用保存的统计特征
            for group_col in ['equip', 'capability', 'recipe']:
                if group_col in self.stat_features:
                    # 为每行添加统计特征
                    for stat in ['mean', 'std', 'count']:
                        col_name = f'{group_col}_rt_{stat}'
                        df_features[col_name] = df_features[group_col].map(
                            lambda x: self.stat_features[group_col].get(x, {}).get(f'{group_col}_rt_{stat}', 0)
                        )

        return df_features
    
    def prepare_training_data(self, df: pd.DataFrame) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """
        准备训练数据
        """
        # 选择基础特征列
        feature_cols = [
            'capability_encoded', 'recipe_encoded', 'equip_encoded', 'sub_equip_encoded',
            'qty_scaled'
        ]

        # 添加统计特征（如果存在）
        stat_cols = [col for col in df.columns if '_rt_' in col]
        feature_cols.extend(stat_cols)

        # 保存最终使用的特征列
        self.final_feature_cols = feature_cols.copy()

        X = df[feature_cols].values
        y = df['rt_scaled'].values if 'rt_scaled' in df.columns and not df['rt_scaled'].isnull().all() else None

        return X, y
    
    def fit_transform(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, pd.DataFrame]:
        """
        拟合并转换训练数据
        """
        # 数据验证和清洗
        df = self.validate_data(df)
        df_with_rt, df_missing_rt = self.clean_data(df)
        
        # 特征工程
        df_with_rt = self.create_features(df_with_rt)
        
        # 编码和标准化
        df_with_rt = self.encode_categorical_features(df_with_rt, fit=True)
        df_with_rt = self.scale_numerical_features(df_with_rt, fit=True)
        
        # 准备训练数据
        X, y = self.prepare_training_data(df_with_rt)

        if y is None:
            raise ValueError("没有有效的目标变量数据")

        self.is_fitted = True
        logger.info(f"训练数据准备完成，特征维度: {X.shape}, 目标维度: {y.shape}")

        return X, y, df_missing_rt
    
    def transform(self, df: pd.DataFrame) -> np.ndarray:
        """
        转换新数据（用于预测）
        """
        if not self.is_fitted:
            raise ValueError("处理器未训练，请先调用fit_transform")

        # 数据验证
        df = self.validate_data(df)

        # 特征工程（不重新计算统计特征）
        df = self.create_features(df, fit=False)

        # 编码和标准化
        df = self.encode_categorical_features(df, fit=False)
        df = self.scale_numerical_features(df, fit=False)

        # 使用保存的特征列确保维度一致
        if not self.final_feature_cols:
            raise ValueError("最终特征列未保存，请重新训练模型")

        # 确保所有需要的列都存在
        missing_cols = [col for col in self.final_feature_cols if col not in df.columns]
        if missing_cols:
            logger.warning(f"缺少特征列: {missing_cols}，将用0填充")
            for col in missing_cols:
                df[col] = 0

        X = df[self.final_feature_cols].values

        return X
    
    def inverse_transform_target(self, y_scaled: np.ndarray) -> np.ndarray:
        """
        反标准化目标变量
        """
        if 'rt' not in self.scalers:
            raise ValueError("rt标准化器不存在")
        return self.scalers['rt'].inverse_transform(y_scaled.reshape(-1, 1)).flatten()
