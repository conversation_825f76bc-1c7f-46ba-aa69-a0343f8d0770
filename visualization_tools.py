"""
半导体制造机台能力数据可视化和分析工具
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class DataAnalyzer:
    """数据分析器"""
    
    def __init__(self, df: pd.DataFrame):
        """
        初始化分析器
        Args:
            df: 数据DataFrame
        """
        self.df = df
        
    def basic_statistics(self) -> Dict:
        """
        基础统计分析
        """
        stats = {
            'data_shape': self.df.shape,
            'missing_values': self.df.isnull().sum().to_dict(),
            'numerical_stats': self.df.describe().to_dict(),
            'categorical_stats': {}
        }
        
        # 分类变量统计
        categorical_cols = ['capability', 'recipe', 'equip', 'sub_equip']
        for col in categorical_cols:
            if col in self.df.columns:
                stats['categorical_stats'][col] = {
                    'unique_count': self.df[col].nunique(),
                    'top_values': self.df[col].value_counts().head().to_dict()
                }
        
        return stats
    
    def analyze_rt_distribution(self) -> Dict:
        """
        分析RT分布
        """
        rt_stats = {
            'overall_stats': self.df['rt'].describe().to_dict(),
            'by_capability': self.df.groupby('capability')['rt'].describe().to_dict(),
            'by_equip': self.df.groupby('equip')['rt'].describe().to_dict(),
            'by_qty_range': {}
        }
        
        # 按qty范围分析
        self.df['qty_range'] = pd.cut(self.df['qty'], bins=5, labels=['1-5', '6-10', '11-15', '16-20', '21-25'])
        rt_stats['by_qty_range'] = self.df.groupby('qty_range')['rt'].describe().to_dict()
        
        return rt_stats
    
    def find_outliers(self, method: str = 'iqr') -> pd.DataFrame:
        """
        发现异常值
        """
        if method == 'iqr':
            Q1 = self.df['rt'].quantile(0.25)
            Q3 = self.df['rt'].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = self.df[(self.df['rt'] < lower_bound) | (self.df['rt'] > upper_bound)]
            
        elif method == 'zscore':
            z_scores = np.abs((self.df['rt'] - self.df['rt'].mean()) / self.df['rt'].std())
            outliers = self.df[z_scores > 3]
        
        return outliers

class Visualizer:
    """可视化工具"""
    
    def __init__(self, df: pd.DataFrame):
        """
        初始化可视化工具
        """
        self.df = df
        
    def plot_rt_distribution(self, save_path: str = None):
        """
        绘制RT分布图
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 整体分布
        axes[0, 0].hist(self.df['rt'], bins=50, alpha=0.7, color='skyblue')
        axes[0, 0].set_title('RT整体分布')
        axes[0, 0].set_xlabel('RT值')
        axes[0, 0].set_ylabel('频次')
        
        # 按capability分布
        capabilities = self.df['capability'].unique()[:5]  # 取前5个
        for i, cap in enumerate(capabilities):
            data = self.df[self.df['capability'] == cap]['rt']
            axes[0, 1].hist(data, alpha=0.5, label=cap, bins=30)
        axes[0, 1].set_title('按Capability的RT分布')
        axes[0, 1].set_xlabel('RT值')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].legend()
        
        # RT vs QTY散点图
        axes[1, 0].scatter(self.df['qty'], self.df['rt'], alpha=0.6, color='coral')
        axes[1, 0].set_title('RT vs QTY关系')
        axes[1, 0].set_xlabel('QTY')
        axes[1, 0].set_ylabel('RT值')
        
        # 箱线图
        equips = self.df['equip'].unique()[:8]  # 取前8个设备
        data_for_box = [self.df[self.df['equip'] == equip]['rt'] for equip in equips]
        axes[1, 1].boxplot(data_for_box, labels=equips)
        axes[1, 1].set_title('按设备的RT分布')
        axes[1, 1].set_xlabel('设备')
        axes[1, 1].set_ylabel('RT值')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_correlation_matrix(self, save_path: str = None):
        """
        绘制相关性矩阵
        """
        # 准备数值数据
        df_numeric = self.df.copy()
        
        # 编码分类变量
        from sklearn.preprocessing import LabelEncoder
        le = LabelEncoder()
        
        categorical_cols = ['capability', 'recipe', 'equip', 'sub_equip']
        for col in categorical_cols:
            if col in df_numeric.columns:
                df_numeric[f'{col}_encoded'] = le.fit_transform(df_numeric[col].astype(str))
        
        # 选择数值列
        numeric_cols = ['qty', 'rt'] + [f'{col}_encoded' for col in categorical_cols if col in df_numeric.columns]
        correlation_matrix = df_numeric[numeric_cols].corr()
        
        # 绘制热力图
        plt.figure(figsize=(10, 8))
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5)
        plt.title('特征相关性矩阵')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_interactive_dashboard(self) -> go.Figure:
        """
        创建交互式仪表板
        """
        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('RT分布', 'RT vs QTY', '按设备的RT分布', '按Capability的RT分布'),
            specs=[[{"type": "histogram"}, {"type": "scatter"}],
                   [{"type": "box"}, {"type": "violin"}]]
        )
        
        # RT分布直方图
        fig.add_trace(
            go.Histogram(x=self.df['rt'], name='RT分布', nbinsx=50),
            row=1, col=1
        )
        
        # RT vs QTY散点图
        fig.add_trace(
            go.Scatter(x=self.df['qty'], y=self.df['rt'], 
                      mode='markers', name='RT vs QTY',
                      marker=dict(color=self.df['rt'], colorscale='viridis')),
            row=1, col=2
        )
        
        # 按设备的箱线图
        equips = self.df['equip'].unique()[:8]
        for equip in equips:
            data = self.df[self.df['equip'] == equip]['rt']
            fig.add_trace(
                go.Box(y=data, name=equip, showlegend=False),
                row=2, col=1
            )
        
        # 按Capability的小提琴图
        capabilities = self.df['capability'].unique()[:6]
        for cap in capabilities:
            data = self.df[self.df['capability'] == cap]['rt']
            fig.add_trace(
                go.Violin(y=data, name=cap, showlegend=False),
                row=2, col=2
            )
        
        # 更新布局
        fig.update_layout(
            title_text="半导体制造机台能力数据分析仪表板",
            height=800,
            showlegend=True
        )
        
        return fig
    
    def plot_prediction_results(self, 
                              y_true: np.ndarray, 
                              y_pred: np.ndarray,
                              save_path: str = None):
        """
        绘制预测结果分析图
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 真实值 vs 预测值散点图
        axes[0, 0].scatter(y_true, y_pred, alpha=0.6, color='blue')
        axes[0, 0].plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--', lw=2)
        axes[0, 0].set_xlabel('真实值')
        axes[0, 0].set_ylabel('预测值')
        axes[0, 0].set_title('真实值 vs 预测值')
        
        # 残差图
        residuals = y_true - y_pred
        axes[0, 1].scatter(y_pred, residuals, alpha=0.6, color='green')
        axes[0, 1].axhline(y=0, color='r', linestyle='--')
        axes[0, 1].set_xlabel('预测值')
        axes[0, 1].set_ylabel('残差')
        axes[0, 1].set_title('残差分布')
        
        # 残差直方图
        axes[1, 0].hist(residuals, bins=50, alpha=0.7, color='orange')
        axes[1, 0].set_xlabel('残差')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].set_title('残差直方图')
        
        # Q-Q图
        from scipy import stats
        stats.probplot(residuals, dist="norm", plot=axes[1, 1])
        axes[1, 1].set_title('残差Q-Q图')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_feature_importance(self, 
                              feature_names: List[str], 
                              importance_scores: np.ndarray,
                              save_path: str = None):
        """
        绘制特征重要性图
        """
        # 排序
        indices = np.argsort(importance_scores)[::-1]
        
        plt.figure(figsize=(12, 8))
        plt.bar(range(len(importance_scores)), importance_scores[indices])
        plt.xticks(range(len(importance_scores)), [feature_names[i] for i in indices], rotation=45)
        plt.xlabel('特征')
        plt.ylabel('重要性分数')
        plt.title('特征重要性分析')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

class ModelPerformanceAnalyzer:
    """模型性能分析器"""
    
    def __init__(self):
        pass
    
    def analyze_training_history(self, history: dict, save_path: str = None):
        """
        分析训练历史
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 损失曲线
        axes[0, 0].plot(history['loss'], label='训练损失')
        axes[0, 0].plot(history['val_loss'], label='验证损失')
        axes[0, 0].set_title('损失曲线')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        
        # MAE曲线
        if 'mae' in history:
            axes[0, 1].plot(history['mae'], label='训练MAE')
            axes[0, 1].plot(history['val_mae'], label='验证MAE')
            axes[0, 1].set_title('MAE曲线')
            axes[0, 1].set_xlabel('Epoch')
            axes[0, 1].set_ylabel('MAE')
            axes[0, 1].legend()
        
        # R²曲线
        if 'r_squared' in history:
            axes[1, 0].plot(history['r_squared'], label='训练R²')
            axes[1, 0].plot(history['val_r_squared'], label='验证R²')
            axes[1, 0].set_title('R²曲线')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('R²')
            axes[1, 0].legend()
        
        # 学习率曲线（如果有）
        if 'lr' in history:
            axes[1, 1].plot(history['lr'])
            axes[1, 1].set_title('学习率曲线')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('Learning Rate')
            axes[1, 1].set_yscale('log')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_performance_report(self, 
                                metrics: Dict[str, float],
                                model_name: str = "模型") -> str:
        """
        创建性能报告
        """
        report = f"""
        {model_name}性能报告
        ==================
        
        回归指标:
        - MAE (平均绝对误差): {metrics.get('mae', 'N/A'):.4f}
        - MSE (均方误差): {metrics.get('mse', 'N/A'):.4f}
        - RMSE (均方根误差): {metrics.get('rmse', 'N/A'):.4f}
        - R² (决定系数): {metrics.get('r2', 'N/A'):.4f}
        - MAPE (平均绝对百分比误差): {metrics.get('mape', 'N/A'):.2f}%
        
        模型解释:
        - R² = {metrics.get('r2', 0):.4f} 表示模型解释了 {metrics.get('r2', 0)*100:.2f}% 的方差
        - MAPE = {metrics.get('mape', 0):.2f}% 表示平均预测误差为 {metrics.get('mape', 0):.2f}%
        
        性能评级:
        """
        
        # 性能评级
        r2 = metrics.get('r2', 0)
        mape = metrics.get('mape', 100)
        
        if r2 > 0.9 and mape < 10:
            report += "- 优秀 (Excellent)"
        elif r2 > 0.8 and mape < 15:
            report += "- 良好 (Good)"
        elif r2 > 0.7 and mape < 20:
            report += "- 一般 (Fair)"
        else:
            report += "- 需要改进 (Needs Improvement)"
        
        return report
