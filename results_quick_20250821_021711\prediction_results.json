{"timestamp": "20250821_021711", "mode": "quick", "model_metrics": {"mae": 357.882381894879, "mse": 146239.55948031173, "rmse": 382.41281291336423, "r2": -68.58531948400343, "mape": 263.4032846634031}, "single_prediction": {"predicted_rt": 459.4514465332031, "confidence": 0.9999317694664769, "input_features": {"capability": "CAP_A", "recipe": "RECIPE_1", "equip": "EQP_001", "sub_equip": "SUB_A", "qty": 15}}, "smart_prediction": {"predicted_rt": 427741.03125, "confidence": 0.9999584946946894, "input_features": {"capability": "CAP_B", "recipe": "RECIPE_2", "equip": "EQP_002", "sub_equip": "SUB_B", "qty": 12}, "method": "direct"}, "training_config": {"model_params": {"hidden_units": [32, 16], "dropout_rate": 0.1, "l2_reg": 0.01}, "training_params": {"epochs": 5, "batch_size": 64, "learning_rate": 0.02, "patience": 3, "verbose": 1}}, "data_info": {"total_samples": 140, "missing_samples": 60, "completed_samples": 182}}