
半导体制造机台能力预测系统 - 结果摘要
==================================================
运行时间: 20250821_021711
运行模式: quick
数据规模: 140 条训练数据

模型性能:
- R² (决定系数): -68.5853
- RMSE (均方根误差): 382.41
- MAE (平均绝对误差): 357.88
- MAPE (平均绝对百分比误差): 263.40%

预测示例:
- 单个预测RT: 459.45
- 预测置信度: 1.000

智能预测:
- 智能预测RT: 427741.03
- 预测方法: direct

文件保存位置:
- 模型文件: ./results_quick_20250821_021711\model
- 结果文件: ./results_quick_20250821_021711\prediction_results.json
- 补全数据: ./results_quick_20250821_021711\completed_data.csv (已排序)
- 原始数据: ./results_quick_20250821_021711\original_data.csv (已排序)

数据排序说明:
所有保存的数据均按以下顺序排序：
1. capability (能力类型)
2. recipe (制程菜单)
3. equip (机台名称)
4. sub_equip (子机台)
5. qty (片数，从小到大)

使用保存的模型进行预测:
from training_pipeline import TrainingPipeline
model, processor = TrainingPipeline().load_model('./results_quick_20250821_021711\model')
