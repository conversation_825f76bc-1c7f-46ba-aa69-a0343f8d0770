"""
半导体制造机台能力预测模型训练管道
包含训练、验证、超参数调优等功能
"""

import tensorflow as tf
from tensorflow import keras
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split, KFold
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import optuna
from typing import Dict, List, Tuple, Optional, Any
import logging
import json
import os
from datetime import datetime

from data_processor import EquipmentDataProcessor
from model_architecture import EquipmentRTPredictor, AdvancedEquipmentPredictor

logger = logging.getLogger(__name__)

class TrainingPipeline:
    """训练管道类"""
    
    def __init__(self, 
                 data_processor: EquipmentDataProcessor,
                 model_type: str = 'standard',
                 random_state: int = 42):
        """
        初始化训练管道
        Args:
            data_processor: 数据处理器
            model_type: 模型类型 ('standard', 'transformer', 'wide_deep')
            random_state: 随机种子
        """
        self.data_processor = data_processor
        self.model_type = model_type
        self.random_state = random_state
        self.model = None
        self.history = None
        self.best_params = None
        
        # 设置随机种子
        tf.random.set_seed(random_state)
        np.random.seed(random_state)
        
    def prepare_data(self, 
                    df: pd.DataFrame,
                    test_size: float = 0.2,
                    val_size: float = 0.2) -> Tuple[np.ndarray, ...]:
        """
        准备训练、验证和测试数据
        """
        # 数据预处理
        X, y, df_missing = self.data_processor.fit_transform(df)
        
        # 分割数据
        X_temp, X_test, y_temp, y_test = train_test_split(
            X, y, test_size=test_size, random_state=self.random_state
        )
        
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp, test_size=val_size/(1-test_size), random_state=self.random_state
        )
        
        logger.info(f"训练集大小: {X_train.shape}")
        logger.info(f"验证集大小: {X_val.shape}")
        logger.info(f"测试集大小: {X_test.shape}")
        
        return X_train, X_val, X_test, y_train, y_val, y_test, df_missing
    
    def create_model(self, **model_params) -> EquipmentRTPredictor:
        """
        创建模型实例
        """
        if self.model_type == 'standard':
            model_class = EquipmentRTPredictor
        elif self.model_type in ['transformer', 'wide_deep']:
            model_class = AdvancedEquipmentPredictor
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")
        
        return model_class(**model_params)
    
    def train_model(self,
                   X_train: np.ndarray,
                   y_train: np.ndarray,
                   X_val: np.ndarray,
                   y_val: np.ndarray,
                   model_params: Dict = None,
                   training_params: Dict = None) -> Dict[str, Any]:
        """
        训练模型
        """
        # 默认参数
        model_params = model_params or {}
        training_params = training_params or {}
        
        # 设置默认模型参数
        default_model_params = {
            'input_dim': X_train.shape[1],
            'hidden_units': [256, 128, 64, 32],
            'dropout_rate': 0.3,
            'l2_reg': 0.001
        }
        default_model_params.update(model_params)
        
        # 设置默认训练参数
        default_training_params = {
            'epochs': 100,
            'batch_size': 32,
            'learning_rate': 0.001,
            'patience': 20,
            'verbose': 1
        }
        default_training_params.update(training_params)
        
        # 创建模型
        predictor = self.create_model(**default_model_params)
        
        # 构建和编译模型
        if self.model_type == 'transformer':
            model = predictor.build_transformer_model()
        elif self.model_type == 'wide_deep':
            model = predictor.build_wide_and_deep_model()
        else:
            model = predictor.build_model()
            
        model = predictor.compile_model(
            model, 
            learning_rate=default_training_params['learning_rate']
        )
        
        # 创建回调函数
        callbacks = predictor.create_callbacks(
            patience=default_training_params['patience']
        )
        
        # 训练模型
        history = model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=default_training_params['epochs'],
            batch_size=default_training_params['batch_size'],
            callbacks=callbacks,
            verbose=default_training_params['verbose']
        )
        
        self.model = model
        self.history = history
        
        # 返回训练结果
        return {
            'model': model,
            'history': history,
            'predictor': predictor
        }
    
    def evaluate_model(self,
                      model: keras.Model,
                      X_test: np.ndarray,
                      y_test: np.ndarray) -> Dict[str, float]:
        """
        评估模型性能
        """
        # 预测
        y_pred = model.predict(X_test)
        
        # 反标准化
        y_test_original = self.data_processor.inverse_transform_target(y_test)
        y_pred_original = self.data_processor.inverse_transform_target(y_pred.flatten())
        
        # 计算指标
        metrics = {
            'mae': mean_absolute_error(y_test_original, y_pred_original),
            'mse': mean_squared_error(y_test_original, y_pred_original),
            'rmse': np.sqrt(mean_squared_error(y_test_original, y_pred_original)),
            'r2': r2_score(y_test_original, y_pred_original),
            'mape': np.mean(np.abs((y_test_original - y_pred_original) / y_test_original)) * 100
        }
        
        logger.info("模型评估结果:")
        for metric, value in metrics.items():
            logger.info(f"{metric.upper()}: {value:.4f}")
        
        return metrics
    
    def cross_validate(self,
                      X: np.ndarray,
                      y: np.ndarray,
                      cv_folds: int = 5,
                      model_params: Dict = None,
                      training_params: Dict = None) -> Dict[str, List[float]]:
        """
        交叉验证
        """
        kfold = KFold(n_splits=cv_folds, shuffle=True, random_state=self.random_state)
        
        cv_scores = {
            'mae': [],
            'mse': [],
            'rmse': [],
            'r2': [],
            'mape': []
        }
        
        for fold, (train_idx, val_idx) in enumerate(kfold.split(X)):
            logger.info(f"训练第 {fold + 1}/{cv_folds} 折")
            
            X_train_fold, X_val_fold = X[train_idx], X[val_idx]
            y_train_fold, y_val_fold = y[train_idx], y[val_idx]
            
            # 训练模型
            result = self.train_model(
                X_train_fold, y_train_fold,
                X_val_fold, y_val_fold,
                model_params, training_params
            )
            
            # 评估模型
            metrics = self.evaluate_model(result['model'], X_val_fold, y_val_fold)
            
            # 记录分数
            for metric, value in metrics.items():
                cv_scores[metric].append(value)
        
        # 计算平均分数和标准差
        cv_summary = {}
        for metric, scores in cv_scores.items():
            cv_summary[f'{metric}_mean'] = np.mean(scores)
            cv_summary[f'{metric}_std'] = np.std(scores)
        
        logger.info("交叉验证结果:")
        for metric, value in cv_summary.items():
            logger.info(f"{metric}: {value:.4f}")
        
        return cv_scores, cv_summary
    
    def hyperparameter_optimization(self,
                                  X_train: np.ndarray,
                                  y_train: np.ndarray,
                                  X_val: np.ndarray,
                                  y_val: np.ndarray,
                                  n_trials: int = 50) -> Dict[str, Any]:
        """
        使用Optuna进行超参数优化
        """
        def objective(trial):
            # 定义超参数搜索空间
            model_params = {
                'input_dim': X_train.shape[1],
                'hidden_units': [
                    trial.suggest_int('hidden_1', 128, 512),
                    trial.suggest_int('hidden_2', 64, 256),
                    trial.suggest_int('hidden_3', 32, 128),
                    trial.suggest_int('hidden_4', 16, 64)
                ],
                'dropout_rate': trial.suggest_float('dropout_rate', 0.1, 0.5),
                'l2_reg': trial.suggest_float('l2_reg', 1e-5, 1e-2, log=True)
            }
            
            training_params = {
                'epochs': 50,  # 减少epochs以加快搜索
                'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64, 128]),
                'learning_rate': trial.suggest_float('learning_rate', 1e-5, 1e-2, log=True),
                'patience': 10,
                'verbose': 0
            }
            
            try:
                # 训练模型
                result = self.train_model(
                    X_train, y_train, X_val, y_val,
                    model_params, training_params
                )
                
                # 评估模型
                metrics = self.evaluate_model(result['model'], X_val, y_val)
                
                # 返回要最小化的目标值（验证集RMSE）
                return metrics['rmse']
                
            except Exception as e:
                logger.warning(f"Trial失败: {e}")
                return float('inf')
        
        # 创建研究
        study = optuna.create_study(direction='minimize')
        study.optimize(objective, n_trials=n_trials)
        
        # 保存最佳参数
        self.best_params = study.best_params
        
        logger.info(f"最佳参数: {self.best_params}")
        logger.info(f"最佳RMSE: {study.best_value:.4f}")
        
        return {
            'best_params': study.best_params,
            'best_value': study.best_value,
            'study': study
        }
    
    def save_model(self, 
                   model: keras.Model,
                   save_path: str,
                   include_processor: bool = True):
        """
        保存模型和相关组件
        """
        # 创建保存目录
        os.makedirs(save_path, exist_ok=True)
        
        # 保存模型
        model_path = os.path.join(save_path, 'model.h5')
        model.save(model_path)
        
        # 保存数据处理器
        if include_processor:
            processor_path = os.path.join(save_path, 'data_processor.pkl')
            import pickle
            with open(processor_path, 'wb') as f:
                pickle.dump(self.data_processor, f)
        
        # 保存训练配置
        config = {
            'model_type': self.model_type,
            'best_params': self.best_params,
            'timestamp': datetime.now().isoformat()
        }
        
        config_path = os.path.join(save_path, 'config.json')
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"模型已保存到: {save_path}")
    
    def load_model(self, load_path: str) -> Tuple[keras.Model, EquipmentDataProcessor]:
        """
        加载模型和相关组件
        """
        # 加载模型
        model_path = os.path.join(load_path, 'model.h5')
        model = keras.models.load_model(model_path)
        
        # 加载数据处理器
        processor_path = os.path.join(load_path, 'data_processor.pkl')
        import pickle
        with open(processor_path, 'rb') as f:
            data_processor = pickle.load(f)
        
        # 加载配置
        config_path = os.path.join(load_path, 'config.json')
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        self.model_type = config.get('model_type', 'standard')
        self.best_params = config.get('best_params')
        
        logger.info(f"模型已从 {load_path} 加载")
        
        return model, data_processor
