# 半导体制造机台能力预测系统配置文件

# 数据配置
data:
  # 数据文件路径
  data_path: "data/equipment_data.csv"
  
  # 特征列
  feature_columns:
    - capability
    - recipe
    - equip
    - sub_equip
    - qty
  
  # 目标列
  target_column: rt
  
  # 数据分割比例
  test_size: 0.2
  val_size: 0.2
  
  # qty范围
  qty_range:
    min: 1
    max: 25
  
  # 数据清洗参数
  cleaning:
    remove_duplicates: true
    handle_outliers: true
    outlier_method: "iqr"  # iqr 或 zscore
    outlier_threshold: 3.0

# 模型配置
model:
  # 模型类型: standard, transformer, wide_deep
  type: "standard"
  
  # 网络架构
  architecture:
    hidden_units: [256, 128, 64, 32]
    dropout_rate: 0.3
    l2_reg: 0.001
    activation: "relu"
  
  # 编译参数
  compile:
    learning_rate: 0.001
    optimizer: "adam"
    loss: "custom"  # custom 或 mse
  
  # 高级模型参数
  advanced:
    use_batch_normalization: true
    use_residual_connections: true
    use_attention: true

# 训练配置
training:
  # 基础训练参数
  epochs: 100
  batch_size: 32
  verbose: 1
  
  # 早停参数
  early_stopping:
    patience: 20
    monitor: "val_loss"
    restore_best_weights: true
  
  # 学习率调度
  lr_scheduler:
    patience: 10
    factor: 0.5
    min_lr: 1e-7
  
  # 交叉验证
  cross_validation:
    enabled: false
    folds: 5
  
  # 模型保存
  model_checkpoint:
    save_best_only: true
    monitor: "val_loss"
    save_path: "models/best_model.h5"

# 超参数优化配置
hyperparameter_optimization:
  enabled: false
  n_trials: 50
  
  # 搜索空间
  search_space:
    hidden_units:
      layer_1: [128, 512]
      layer_2: [64, 256]
      layer_3: [32, 128]
      layer_4: [16, 64]
    dropout_rate: [0.1, 0.5]
    l2_reg: [1e-5, 1e-2]
    learning_rate: [1e-5, 1e-2]
    batch_size: [16, 32, 64, 128]

# 预测配置
prediction:
  # 置信度阈值
  confidence_threshold: 0.7
  
  # 数据补全参数
  completion:
    min_coverage: 0.8
    interpolation_weight: 0.3
    direct_prediction_weight: 0.7
  
  # 批量预测
  batch_prediction:
    chunk_size: 1000
    parallel_processing: false

# 可视化配置
visualization:
  # 图表样式
  style: "seaborn"
  figure_size: [12, 8]
  dpi: 300
  
  # 颜色配置
  colors:
    primary: "#1f77b4"
    secondary: "#ff7f0e"
    success: "#2ca02c"
    warning: "#d62728"
  
  # 保存配置
  save_plots: true
  plot_directory: "plots/"
  plot_format: "png"

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/equipment_prediction.log"
  max_file_size: "10MB"
  backup_count: 5

# 性能配置
performance:
  # GPU配置
  gpu:
    enabled: true
    memory_growth: true
    memory_limit: null  # MB, null表示不限制
  
  # 并行处理
  parallel:
    n_jobs: -1  # -1表示使用所有CPU核心
    backend: "threading"
  
  # 内存优化
  memory:
    low_memory_mode: false
    chunk_processing: false
    chunk_size: 10000

# 输出配置
output:
  # 模型保存
  model_save_path: "saved_models/"
  include_processor: true
  
  # 结果保存
  results_path: "results/"
  save_predictions: true
  save_metrics: true
  save_plots: true
  
  # 报告生成
  generate_report: true
  report_format: "html"  # html, pdf, markdown

# 验证配置
validation:
  # 数据验证
  data_validation:
    enabled: true
    check_missing_values: true
    check_data_types: true
    check_value_ranges: true
  
  # 模型验证
  model_validation:
    enabled: true
    check_overfitting: true
    check_underfitting: true
    performance_threshold:
      min_r2: 0.7
      max_mape: 20.0

# 实验配置
experiment:
  # 实验跟踪
  tracking:
    enabled: false
    backend: "mlflow"  # mlflow, wandb, tensorboard
    experiment_name: "equipment_rt_prediction"
  
  # 随机种子
  random_seed: 42
  
  # 实验标签
  tags:
    - "semiconductor"
    - "manufacturing"
    - "rt_prediction"
