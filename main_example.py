"""
半导体制造机台能力预测系统主要使用示例
演示完整的数据处理、模型训练和预测流程

使用说明:
1. 请先激活conda环境: conda activate tfgpu
2. 快速测试模式: python main_example.py --quick (2-5分钟)
3. 简单示例: python main_example.py --simple (30-60秒)
4. 查看已保存结果: python main_example.py --view
5. 完整演示: python main_example.py (15-20分钟)

性能优化:
- 快速模式使用更小的网络结构和更少的训练轮数
- 跳过耗时的可视化和详细分析
- 减少数据补全范围以提高速度

数据排序:
- 所有生成和保存的数据均按以下顺序排序：
  capability → recipe → equip → sub_equip → qty (从小到大)
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from data_processor import EquipmentDataProcessor
from model_architecture import EquipmentRTPredictor, AdvancedEquipmentPredictor
from training_pipeline import TrainingPipeline
from prediction_engine import RTPredictor, DataCompletion, SmartPredictor
from visualization_tools import DataAnalyzer, Visualizer, ModelPerformanceAnalyzer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def view_saved_results():
    """
    查看已保存的结果
    """
    import os
    import json

    print("📁 查看已保存的结果")
    print("="*50)

    # 查找结果目录
    result_dirs = [d for d in os.listdir('.') if d.startswith('results_') or d.startswith('quick_test_results_')]

    if not result_dirs:
        print("❌ 未找到保存的结果")
        print("💡 请先运行: python main_example.py --quick")
        return

    # 按时间排序，显示最新的结果
    result_dirs.sort(reverse=True)

    for i, dir_name in enumerate(result_dirs[:5]):  # 只显示最新的5个
        print(f"\n{i+1}. {dir_name}")

        # 尝试读取结果摘要
        summary_file = os.path.join(dir_name, 'summary.txt')
        if os.path.exists(summary_file):
            with open(summary_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                # 显示前几行关键信息
                for line in lines[:10]:
                    if any(keyword in line for keyword in ['运行时间', '运行模式', '数据规模', 'R²', 'RMSE']):
                        print(f"   {line.strip()}")

        # 显示文件列表
        files = os.listdir(dir_name)
        print(f"   📄 文件: {', '.join(files)}")

    print(f"\n💡 查看详细结果: cat {result_dirs[0]}/summary.txt")
    print(f"🔄 重新加载模型: 参考 {result_dirs[0]}/summary.txt 中的代码")
    print("📊 所有数据文件均已按 capability→recipe→equip→sub_equip→qty 排序")

def generate_sample_data(n_samples: int = 200) -> tuple[pd.DataFrame, pd.DataFrame]:
    """
    生成示例数据用于演示
    """
    np.random.seed(42)
    
    # 定义可能的值
    capabilities = ['CAP_A', 'CAP_B', 'CAP_C', 'CAP_D']
    recipes = ['RECIPE_1', 'RECIPE_2', 'RECIPE_3', 'RECIPE_4', 'RECIPE_5']
    equips = ['EQP_001', 'EQP_002', 'EQP_003', 'EQP_004', 'EQP_005']
    sub_equips = ['SUB_A', 'SUB_B', 'SUB_C']
    
    data = []
    for _ in range(n_samples):
        capability = np.random.choice(capabilities)
        recipe = np.random.choice(recipes)
        equip = np.random.choice(equips)
        sub_equip = np.random.choice(sub_equips)
        qty = np.random.randint(1, 26)
        
        # 模拟RT值，考虑各种因素的影响
        base_rt = 100  # 基础时间
        
        # capability影响
        cap_factor = {'CAP_A': 1.0, 'CAP_B': 1.2, 'CAP_C': 0.8, 'CAP_D': 1.1}[capability]
        
        # recipe影响
        recipe_factor = {
            'RECIPE_1': 1.0, 'RECIPE_2': 1.3, 'RECIPE_3': 0.9, 
            'RECIPE_4': 1.1, 'RECIPE_5': 1.4
        }[recipe]
        
        # equip影响
        equip_factor = {
            'EQP_001': 1.0, 'EQP_002': 0.95, 'EQP_003': 1.05,
            'EQP_004': 0.9, 'EQP_005': 1.1
        }[equip]
        
        # qty影响（非线性关系）
        qty_factor = 1 + (qty - 1) * 0.02 + (qty - 1) ** 2 * 0.0001
        
        # 计算RT
        rt = base_rt * cap_factor * recipe_factor * equip_factor * qty_factor
        
        # 添加噪声
        rt += np.random.normal(0, rt * 0.1)
        
        data.append({
            'capability': capability,
            'recipe': recipe,
            'equip': equip,
            'sub_equip': sub_equip,
            'qty': qty,
            'rt': max(rt, 10)  # 确保RT为正值
        })
    
    df = pd.DataFrame(data)

    # 对生成的数据进行排序
    df = df.sort_values(
        by=['capability', 'recipe', 'equip', 'sub_equip', 'qty'],
        ascending=True
    ).reset_index(drop=True)

    # 随机删除一些记录以模拟缺失数据
    missing_indices = np.random.choice(df.index, size=int(0.3 * len(df)), replace=False)
    df_complete = df.drop(missing_indices.tolist()).reset_index(drop=True)
    df_missing = df.loc[missing_indices.tolist()].copy().reset_index(drop=True)
    df_missing['rt'] = np.nan

    # 对分割后的数据也进行排序
    df_complete = df_complete.sort_values(
        by=['capability', 'recipe', 'equip', 'sub_equip', 'qty'],
        ascending=True
    ).reset_index(drop=True)

    df_missing = df_missing.sort_values(
        by=['capability', 'recipe', 'equip', 'sub_equip', 'qty'],
        ascending=True
    ).reset_index(drop=True)

    logger.info(f"生成完整数据: {len(df_complete)} 条（已排序）")
    logger.info(f"生成缺失数据: {len(df_missing)} 条（已排序）")

    return df_complete, df_missing

def main_workflow(quick_mode=False):
    """
    主要工作流程演示
    Args:
        quick_mode: 是否使用快速模式（减少训练时间）
    """
    mode_text = "快速模式" if quick_mode else "完整模式"
    logger.info(f"开始半导体制造机台能力预测系统演示 - {mode_text}")
    
    # 1. 生成示例数据
    logger.info("=" * 50)
    logger.info("步骤1: 生成示例数据")

    # 根据模式调整数据量
    if quick_mode:
        data_size = 200
        logger.info("快速模式：使用200条数据")
    else:
        data_size = 500
        logger.info("完整模式：使用500条数据")

    df_complete, df_missing = generate_sample_data(data_size)
    
    # 2. 数据分析
    logger.info("=" * 50)
    logger.info("步骤2: 数据分析")

    if quick_mode:
        # 快速模式：只做基础分析
        logger.info("快速模式：跳过详细数据分析")
        logger.info(f"数据形状: {df_complete.shape}")
        logger.info(f"RT统计: 均值={df_complete['rt'].mean():.2f}, 标准差={df_complete['rt'].std():.2f}")
    else:
        # 完整模式：详细分析
        analyzer = DataAnalyzer(df_complete)

        # 基础统计
        basic_stats = analyzer.basic_statistics()
        logger.info(f"数据形状: {basic_stats['data_shape']}")
        logger.info(f"数值特征统计: {basic_stats['numerical_stats']['rt']}")

        # RT分布分析
        rt_stats = analyzer.analyze_rt_distribution()
        logger.info(f"RT整体统计: {rt_stats['overall_stats']}")

        # 异常值检测
        outliers = analyzer.find_outliers()
        logger.info(f"发现异常值: {len(outliers)} 条")

    # 3. 数据可视化
    logger.info("=" * 50)
    logger.info("步骤3: 数据可视化")

    if quick_mode:
        logger.info("快速模式：跳过可视化（节省时间）")
    else:
        visualizer = Visualizer(df_complete)

        # 绘制分布图
        visualizer.plot_rt_distribution()

        # 绘制相关性矩阵
        visualizer.plot_correlation_matrix()
    
    # 4. 数据预处理和模型训练
    logger.info("=" * 50)
    logger.info("步骤4: 模型训练")
    
    # 初始化数据处理器
    data_processor = EquipmentDataProcessor()
    
    # 初始化训练管道
    pipeline = TrainingPipeline(data_processor, model_type='standard')
    
    # 准备数据
    X_train, X_val, X_test, y_train, y_val, y_test, df_missing_processed = pipeline.prepare_data(df_complete)
    
    # 根据模式选择训练参数
    if quick_mode:
        model_params = {
            'hidden_units': [32, 16],  # 进一步减少网络大小
            'dropout_rate': 0.1,
            'l2_reg': 0.01
        }
        training_params = {
            'epochs': 5,  # 进一步减少训练轮数
            'batch_size': 64,  # 增加批次大小
            'learning_rate': 0.02,  # 进一步提高学习率
            'patience': 3,  # 进一步减少早停耐心
            'verbose': 1
        }
        logger.info("使用超快速训练配置（5轮训练）")
    else:
        model_params = {
            'hidden_units': [128, 64, 32],  # 适中的网络大小
            'dropout_rate': 0.3,
            'l2_reg': 0.001
        }
        training_params = {
            'epochs': 30,  # 适中的训练轮数
            'batch_size': 32,
            'learning_rate': 0.001,
            'patience': 10,  # 适中的耐心
            'verbose': 1
        }
        logger.info("使用优化的完整训练配置（30轮训练）")

    # 训练模型
    training_result = pipeline.train_model(
        X_train, y_train, X_val, y_val,
        model_params,
        training_params
    )
    
    model = training_result['model']
    history = training_result['history']
    
    # 5. 模型评估
    logger.info("=" * 50)
    logger.info("步骤5: 模型评估")

    metrics = pipeline.evaluate_model(model, X_test, y_test)

    if quick_mode:
        # 快速模式：只显示关键指标
        logger.info(f"快速评估结果: R² = {metrics['r2']:.3f}, RMSE = {metrics['rmse']:.2f}, MAPE = {metrics['mape']:.1f}%")
    else:
        # 完整模式：详细分析
        # 性能分析
        perf_analyzer = ModelPerformanceAnalyzer()
        perf_analyzer.analyze_training_history(history.history)

        # 生成性能报告
        report = perf_analyzer.create_performance_report(metrics, "半导体制造RT预测模型")
        print(report)

        # 预测结果可视化
        y_pred = model.predict(X_test)
        y_test_original = data_processor.inverse_transform_target(y_test)
        y_pred_original = data_processor.inverse_transform_target(y_pred.flatten())

        visualizer.plot_prediction_results(y_test_original, y_pred_original)
    
    # 6. 预测和数据补全
    logger.info("=" * 50)
    logger.info("步骤6: 预测和数据补全")
    
    # 创建预测器
    predictor = RTPredictor(model, data_processor)
    
    # 单个预测示例
    single_prediction = predictor.predict_single(
        capability='CAP_A',
        recipe='RECIPE_1',
        equip='EQP_001',
        sub_equip='SUB_A',
        qty=15
    )
    
    logger.info(f"单个预测结果: {single_prediction}")
    
    # 批量预测
    if len(df_missing) > 0:
        test_size = 3 if quick_mode else 10
        batch_predictions = predictor.predict_batch(df_missing.head(test_size))
        logger.info(f"批量预测完成，预测了 {len(batch_predictions)} 条记录")

    # 数据补全
    if quick_mode:
        # 快速模式：减少补全范围
        data_completion = DataCompletion(predictor, qty_range=(1, 10))
        logger.info("快速模式：使用简化的数据补全（qty 1-10）")
        # 只补全少量数据
        test_data = df_complete.head(20)
        completed_data = data_completion.complete_partial_combinations(test_data, min_coverage=0.5)
        logger.info(f"快速数据补全完成，从 {len(test_data)} 条增加到 {len(completed_data)} 条")
    else:
        # 完整模式：完整补全
        data_completion = DataCompletion(predictor, qty_range=(1, 25))
        completed_data = data_completion.complete_partial_combinations(df_complete, min_coverage=0.8)
        logger.info(f"完整数据补全完成，总记录数: {len(completed_data)}")

    # 7. 智能预测
    logger.info("=" * 50)
    logger.info("步骤7: 智能预测")

    smart_predictor = SmartPredictor(predictor, data_completion)

    # 结合插值的预测
    smart_prediction = smart_predictor.predict_with_interpolation(
        capability='CAP_B',
        recipe='RECIPE_2',
        equip='EQP_002',
        sub_equip='SUB_B',
        qty=12,
        df=df_complete
    )
    
    logger.info(f"智能预测结果: {smart_prediction}")
    
    # 8. 保存结果和模型
    logger.info("=" * 50)
    logger.info("步骤8: 保存结果和模型")

    import os
    import json
    from datetime import datetime

    # 创建结果保存目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    mode_suffix = "quick" if quick_mode else "full"
    save_dir = f'./results_{mode_suffix}_{timestamp}'
    os.makedirs(save_dir, exist_ok=True)

    # 保存模型
    model_dir = os.path.join(save_dir, 'model')
    pipeline.save_model(model, model_dir, include_processor=True)
    logger.info(f"模型已保存到: {model_dir}")

    # 保存预测结果
    results = {
        'timestamp': timestamp,
        'mode': mode_suffix,
        'model_metrics': metrics,
        'single_prediction': single_prediction,
        'smart_prediction': smart_prediction,
        'training_config': {
            'model_params': model_params,
            'training_params': training_params
        },
        'data_info': {
            'total_samples': len(df_complete),
            'missing_samples': len(df_missing),
            'completed_samples': len(completed_data) if 'completed_data' in locals() else 0
        }
    }

    # 保存为JSON文件
    results_file = os.path.join(save_dir, 'prediction_results.json')
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=str)
    logger.info(f"预测结果已保存到: {results_file}")

    # 保存补全后的数据（排序）
    if 'completed_data' in locals():
        completed_file = os.path.join(save_dir, 'completed_data.csv')
        # 按照指定列进行排序
        completed_data_sorted = completed_data.sort_values(
            by=['capability', 'recipe', 'equip', 'sub_equip', 'qty'],
            ascending=True
        ).reset_index(drop=True)
        completed_data_sorted.to_csv(completed_file, index=False, encoding='utf-8-sig')
        logger.info(f"补全数据已保存到: {completed_file} (已按capability,recipe,equip,sub_equip,qty排序)")

    # 保存原始数据（排序）
    original_file = os.path.join(save_dir, 'original_data.csv')
    df_complete_sorted = df_complete.sort_values(
        by=['capability', 'recipe', 'equip', 'sub_equip', 'qty'],
        ascending=True
    ).reset_index(drop=True)
    df_complete_sorted.to_csv(original_file, index=False, encoding='utf-8-sig')
    logger.info(f"原始数据已保存到: {original_file} (已按capability,recipe,equip,sub_equip,qty排序)")

    # 创建结果摘要
    summary = f"""
半导体制造机台能力预测系统 - 结果摘要
{'='*50}
运行时间: {timestamp}
运行模式: {mode_suffix}
数据规模: {len(df_complete)} 条训练数据

模型性能:
- R² (决定系数): {metrics['r2']:.4f}
- RMSE (均方根误差): {metrics['rmse']:.2f}
- MAE (平均绝对误差): {metrics['mae']:.2f}
- MAPE (平均绝对百分比误差): {metrics['mape']:.2f}%

预测示例:
- 单个预测RT: {single_prediction['predicted_rt']:.2f}
- 预测置信度: {single_prediction['confidence']:.3f}

智能预测:
- 智能预测RT: {smart_prediction['predicted_rt']:.2f}
- 预测方法: {smart_prediction.get('method', 'direct')}

文件保存位置:
- 模型文件: {model_dir}
- 结果文件: {results_file}
- 补全数据: {completed_file if 'completed_data' in locals() else '未生成'} (已排序)
- 原始数据: {original_file} (已排序)

数据排序说明:
所有保存的数据均按以下顺序排序：
1. capability (能力类型)
2. recipe (制程菜单)
3. equip (机台名称)
4. sub_equip (子机台)
5. qty (片数，从小到大)

使用保存的模型进行预测:
from training_pipeline import TrainingPipeline
model, processor = TrainingPipeline().load_model('{model_dir}')
"""

    summary_file = os.path.join(save_dir, 'summary.txt')
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(summary)

    print("\n" + "="*60)
    print("🎉 运行完成！结果已保存")
    print("="*60)
    print(summary)
    print(f"\n📁 所有结果保存在目录: {save_dir}")
    print("📋 查看详细结果: cat " + summary_file.replace('\\', '/'))
    print("🔄 重新加载模型: 参考summary.txt中的代码")
    
    # 9. 超参数优化示例（可选）
    logger.info("=" * 50)
    logger.info("步骤9: 超参数优化（演示）")
    
    # 注意：这会花费较长时间，实际使用时可以调整n_trials
    # optimization_result = pipeline.hyperparameter_optimization(
    #     X_train, y_train, X_val, y_val, n_trials=10
    # )
    # logger.info(f"最佳超参数: {optimization_result['best_params']}")
    
    logger.info("🎉 演示完成！")

    # 最终总结
    print("\n" + "🎯 " + "="*58)
    print("📋 运行完成总结")
    print("="*60)
    print(f"✅ 运行模式: {'快速模式' if quick_mode else '完整模式'}")
    print(f"✅ 数据规模: {len(df_complete)} 条训练数据")
    print(f"✅ 模型性能: R² = {metrics['r2']:.3f}, RMSE = {metrics['rmse']:.2f}")
    print(f"✅ 预测示例: RT = {single_prediction['predicted_rt']:.2f}")
    print(f"✅ 所有结果已保存到: {save_dir}")
    print("="*60)
    print("💡 提示: 查看 summary.txt 了解如何重新加载和使用模型")
    print("🔍 查看结果: python -c \"import os; print('\\n'.join([d for d in os.listdir('.') if d.startswith('results_')]))\"")
    print("📊 数据排序: 所有结果已按 capability→recipe→equip→sub_equip→qty 排序")

    return save_dir  # 返回保存目录路径

def quick_prediction_example():
    """
    快速预测示例 - 超快速验证功能
    """
    import time
    start_time = time.time()

    logger.info("快速预测示例 - 超快速模式")

    # 生成最少数据
    df_complete, _ = generate_sample_data(100)
    logger.info(f"生成数据: {df_complete.shape}")

    # 超快速训练配置
    data_processor = EquipmentDataProcessor()
    pipeline = TrainingPipeline(data_processor)

    X_train, X_val, X_test, y_train, y_val, y_test, _ = pipeline.prepare_data(df_complete)

    result = pipeline.train_model(
        X_train, y_train, X_val, y_val,
        model_params={'hidden_units': [16], 'dropout_rate': 0.1},
        training_params={'epochs': 3, 'batch_size': 32, 'verbose': 0}
    )

    # 创建预测器并预测
    predictor = RTPredictor(result['model'], data_processor)

    prediction = predictor.predict_single(
        capability='CAP_A',
        recipe='RECIPE_1',
        equip='EQP_001',
        sub_equip='SUB_A',
        qty=10
    )

    end_time = time.time()
    elapsed_time = end_time - start_time

    # 保存快速测试结果
    import os
    import json
    from datetime import datetime

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_dir = f'./quick_test_results_{timestamp}'
    os.makedirs(save_dir, exist_ok=True)

    # 保存预测结果
    quick_results = {
        'timestamp': timestamp,
        'mode': 'ultra_quick',
        'elapsed_time': elapsed_time,
        'prediction': prediction,
        'data_shape': df_complete.shape,
        'performance_status': 'optimized' if elapsed_time < 60 else 'needs_optimization'
    }

    results_file = os.path.join(save_dir, 'quick_test_results.json')
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(quick_results, f, indent=2, ensure_ascii=False, default=str)

    # 保存测试数据（排序）
    data_file = os.path.join(save_dir, 'test_data.csv')
    df_complete_sorted = df_complete.sort_values(
        by=['capability', 'recipe', 'equip', 'sub_equip', 'qty'],
        ascending=True
    ).reset_index(drop=True)
    df_complete_sorted.to_csv(data_file, index=False, encoding='utf-8-sig')

    print(f"✅ 超快速测试完成！")
    print(f"预测结果: RT = {prediction['predicted_rt']:.2f}")
    print(f"置信度: {prediction['confidence']:.3f}")
    print(f"总耗时: {elapsed_time:.1f} 秒")
    print(f"📁 结果已保存到: {save_dir}")

    if elapsed_time < 60:
        print("🎉 性能优化成功！")
    else:
        print("⚠️ 仍需进一步优化")

if __name__ == "__main__":
    import sys
    import os

    # 检查conda环境
    print("=" * 60)
    print("半导体制造机台能力预测系统")
    print("=" * 60)

    # 检查是否在正确的conda环境中
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'base')
    print(f"当前conda环境: {conda_env}")

    if conda_env != 'tfgpu':
        print("⚠️  建议使用tfgpu环境以获得最佳性能")
        print("请先运行: conda activate tfgpu")
        print("然后再执行此脚本")
        print()

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == '--quick':
        # 快速模式
        print("🚀 启动快速模式（推荐用于调试）...")
        main_workflow(quick_mode=True)
    elif len(sys.argv) > 1 and sys.argv[1] == '--simple':
        # 简单示例
        print("⚡ 运行简单示例...")
        quick_prediction_example()
    elif len(sys.argv) > 1 and sys.argv[1] == '--view':
        # 查看已保存的结果
        view_saved_results()
        sys.exit(0)
    else:
        # 完整演示
        print("📊 运行完整演示...")
        print("💡 提示:")
        print("   python main_example.py --quick   # 快速模式（2-5分钟）")
        print("   python main_example.py --simple  # 简单示例（30-60秒）")
        print("   python main_example.py --view    # 查看已保存的结果")
        print()

        # 询问用户是否继续完整模式
        try:
            print("⏱️  预计时间对比:")
            print("   - 快速模式: 2-5分钟")
            print("   - 完整模式: 15-20分钟")
            user_input = input("是否继续完整模式？(y/N): ").strip().lower()
            if user_input not in ['y', 'yes']:
                print("已取消。建议使用快速模式: python main_example.py --quick")
                sys.exit(0)
        except KeyboardInterrupt:
            print("\n已取消执行")
            sys.exit(0)

        main_workflow(quick_mode=False)
