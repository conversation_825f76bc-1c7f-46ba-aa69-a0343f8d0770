"""
半导体制造机台能力预测系统主要使用示例
演示完整的数据处理、模型训练和预测流程
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from data_processor import EquipmentDataProcessor
from model_architecture import EquipmentRTPredictor, AdvancedEquipmentPredictor
from training_pipeline import TrainingPipeline
from prediction_engine import RTPredictor, DataCompletion, SmartPredictor
from visualization_tools import DataAnalyzer, Visualizer, ModelPerformanceAnalyzer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_sample_data(n_samples: int = 1000) -> tuple[pd.DataFrame, pd.DataFrame]:
    """
    生成示例数据用于演示
    """
    np.random.seed(42)
    
    # 定义可能的值
    capabilities = ['CAP_A', 'CAP_B', 'CAP_C', 'CAP_D']
    recipes = ['RECIPE_1', 'RECIPE_2', 'RECIPE_3', 'RECIPE_4', 'RECIPE_5']
    equips = ['EQP_001', 'EQP_002', 'EQP_003', 'EQP_004', 'EQP_005']
    sub_equips = ['SUB_A', 'SUB_B', 'SUB_C']
    
    data = []
    for _ in range(n_samples):
        capability = np.random.choice(capabilities)
        recipe = np.random.choice(recipes)
        equip = np.random.choice(equips)
        sub_equip = np.random.choice(sub_equips)
        qty = np.random.randint(1, 26)
        
        # 模拟RT值，考虑各种因素的影响
        base_rt = 100  # 基础时间
        
        # capability影响
        cap_factor = {'CAP_A': 1.0, 'CAP_B': 1.2, 'CAP_C': 0.8, 'CAP_D': 1.1}[capability]
        
        # recipe影响
        recipe_factor = {
            'RECIPE_1': 1.0, 'RECIPE_2': 1.3, 'RECIPE_3': 0.9, 
            'RECIPE_4': 1.1, 'RECIPE_5': 1.4
        }[recipe]
        
        # equip影响
        equip_factor = {
            'EQP_001': 1.0, 'EQP_002': 0.95, 'EQP_003': 1.05,
            'EQP_004': 0.9, 'EQP_005': 1.1
        }[equip]
        
        # qty影响（非线性关系）
        qty_factor = 1 + (qty - 1) * 0.02 + (qty - 1) ** 2 * 0.0001
        
        # 计算RT
        rt = base_rt * cap_factor * recipe_factor * equip_factor * qty_factor
        
        # 添加噪声
        rt += np.random.normal(0, rt * 0.1)
        
        data.append({
            'capability': capability,
            'recipe': recipe,
            'equip': equip,
            'sub_equip': sub_equip,
            'qty': qty,
            'rt': max(rt, 10)  # 确保RT为正值
        })
    
    df = pd.DataFrame(data)
    
    # 随机删除一些记录以模拟缺失数据
    missing_indices = np.random.choice(df.index, size=int(0.3 * len(df)), replace=False)
    df_complete = df.drop(missing_indices.tolist())
    df_missing = df.loc[missing_indices.tolist()].copy()
    df_missing['rt'] = np.nan
    
    logger.info(f"生成完整数据: {len(df_complete)} 条")
    logger.info(f"生成缺失数据: {len(df_missing)} 条")
    
    return df_complete, df_missing

def main_workflow():
    """
    主要工作流程演示
    """
    logger.info("开始半导体制造机台能力预测系统演示")
    
    # 1. 生成示例数据
    logger.info("=" * 50)
    logger.info("步骤1: 生成示例数据")
    df_complete, df_missing = generate_sample_data(200)
    
    # 2. 数据分析
    logger.info("=" * 50)
    logger.info("步骤2: 数据分析")
    analyzer = DataAnalyzer(df_complete)
    
    # 基础统计
    basic_stats = analyzer.basic_statistics()
    logger.info(f"数据形状: {basic_stats['data_shape']}")
    logger.info(f"数值特征统计: {basic_stats['numerical_stats']['rt']}")
    
    # RT分布分析
    rt_stats = analyzer.analyze_rt_distribution()
    logger.info(f"RT整体统计: {rt_stats['overall_stats']}")
    
    # 异常值检测
    outliers = analyzer.find_outliers()
    logger.info(f"发现异常值: {len(outliers)} 条")
    
    # 3. 数据可视化
    logger.info("=" * 50)
    logger.info("步骤3: 数据可视化")
    visualizer = Visualizer(df_complete)
    
    # 绘制分布图
    visualizer.plot_rt_distribution()
    
    # 绘制相关性矩阵
    visualizer.plot_correlation_matrix()
    
    # 4. 数据预处理和模型训练
    logger.info("=" * 50)
    logger.info("步骤4: 模型训练")
    
    # 初始化数据处理器
    data_processor = EquipmentDataProcessor()
    
    # 初始化训练管道
    pipeline = TrainingPipeline(data_processor, model_type='standard')
    
    # 准备数据
    X_train, X_val, X_test, y_train, y_val, y_test, df_missing_processed = pipeline.prepare_data(df_complete)
    
    # 训练模型
    training_result = pipeline.train_model(
        X_train, y_train, X_val, y_val,
        model_params={
            'hidden_units': [256, 128, 64, 32],
            'dropout_rate': 0.3,
            'l2_reg': 0.001
        },
        training_params={
            'epochs': 50,
            'batch_size': 32,
            'learning_rate': 0.001,
            'patience': 10
        }
    )
    
    model = training_result['model']
    history = training_result['history']
    
    # 5. 模型评估
    logger.info("=" * 50)
    logger.info("步骤5: 模型评估")
    
    metrics = pipeline.evaluate_model(model, X_test, y_test)
    
    # 性能分析
    perf_analyzer = ModelPerformanceAnalyzer()
    perf_analyzer.analyze_training_history(history.history)
    
    # 生成性能报告
    report = perf_analyzer.create_performance_report(metrics, "半导体制造RT预测模型")
    print(report)
    
    # 预测结果可视化
    y_pred = model.predict(X_test)
    y_test_original = data_processor.inverse_transform_target(y_test)
    y_pred_original = data_processor.inverse_transform_target(y_pred.flatten())
    
    visualizer.plot_prediction_results(y_test_original, y_pred_original)
    
    # 6. 预测和数据补全
    logger.info("=" * 50)
    logger.info("步骤6: 预测和数据补全")
    
    # 创建预测器
    predictor = RTPredictor(model, data_processor)
    
    # 单个预测示例
    single_prediction = predictor.predict_single(
        capability='CAP_A',
        recipe='RECIPE_1',
        equip='EQP_001',
        sub_equip='SUB_A',
        qty=15
    )
    
    logger.info(f"单个预测结果: {single_prediction}")
    
    # 批量预测
    if len(df_missing) > 0:
        batch_predictions = predictor.predict_batch(df_missing.head(10))
        logger.info(f"批量预测完成，预测了 {len(batch_predictions)} 条记录")
    
    # 数据补全
    data_completion = DataCompletion(predictor, qty_range=(1, 25))
    
    # 补全部分组合
    completed_data = data_completion.complete_partial_combinations(df_complete, min_coverage=0.8)
    logger.info(f"数据补全完成，总记录数: {len(completed_data)}")
    
    # 7. 智能预测
    logger.info("=" * 50)
    logger.info("步骤7: 智能预测")
    
    smart_predictor = SmartPredictor(predictor, data_completion)
    
    # 结合插值的预测
    smart_prediction = smart_predictor.predict_with_interpolation(
        capability='CAP_B',
        recipe='RECIPE_2',
        equip='EQP_002',
        sub_equip='SUB_B',
        qty=12,
        df=df_complete
    )
    
    logger.info(f"智能预测结果: {smart_prediction}")
    
    # 8. 模型保存
    logger.info("=" * 50)
    logger.info("步骤8: 模型保存")
    
    pipeline.save_model(model, './saved_model', include_processor=True)
    logger.info("模型已保存到 ./saved_model")
    
    # 9. 超参数优化示例（可选）
    logger.info("=" * 50)
    logger.info("步骤9: 超参数优化（演示）")
    
    # 注意：这会花费较长时间，实际使用时可以调整n_trials
    # optimization_result = pipeline.hyperparameter_optimization(
    #     X_train, y_train, X_val, y_val, n_trials=10
    # )
    # logger.info(f"最佳超参数: {optimization_result['best_params']}")
    
    logger.info("演示完成！")

def quick_prediction_example():
    """
    快速预测示例
    """
    logger.info("快速预测示例")
    
    # 生成少量数据用于快速演示
    df_complete, df_missing = generate_sample_data(200)
    
    # 快速训练
    data_processor = EquipmentDataProcessor()
    pipeline = TrainingPipeline(data_processor)
    
    X_train, X_val, X_test, y_train, y_val, y_test, _ = pipeline.prepare_data(df_complete)
    
    result = pipeline.train_model(
        X_train, y_train, X_val, y_val,
        training_params={'epochs': 20, 'verbose': 0}
    )
    
    # 创建预测器并预测
    predictor = RTPredictor(result['model'], data_processor)
    
    prediction = predictor.predict_single(
        capability='CAP_A',
        recipe='RECIPE_1', 
        equip='EQP_001',
        sub_equip='SUB_A',
        qty=10
    )
    
    print(f"预测结果: RT = {prediction['predicted_rt']:.2f}, 置信度 = {prediction['confidence']:.3f}")

if __name__ == "__main__":
    # 运行完整演示
    main_workflow()

    # 或者运行快速示例
    # quick_prediction_example()
