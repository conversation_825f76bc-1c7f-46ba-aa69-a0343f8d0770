# 半导体制造机台能力预测系统

基于TensorFlow 2的半导体制造机台RT（运行时间）预测和数据补全系统。

## 🎯 项目概述

本系统专为半导体制造领域设计，用于预测机台在不同配置下的运行时间（RT），并补全缺失的数据组合。系统考虑了以下关键因素：

- **Capability**: 机台能力类型
- **Recipe**: 制程菜单
- **Equip**: 机台名称  
- **Sub_equip**: 机台子设备
- **Qty**: WIP片数（1-25片）
- **RT**: 对应的运行时间（预测目标）

## 🚀 主要功能

### 1. 数据预处理
- 自动数据清洗和验证
- 分类特征编码
- 数值特征标准化
- 特征工程和组合特征生成

### 2. 模型架构
- **标准深度神经网络**: 多层感知机with残差连接
- **Transformer模型**: 适用于复杂特征交互
- **Wide & Deep模型**: 结合线性和深度学习优势
- 自定义损失函数，考虑半导体制造特点

### 3. 预测功能
- 单个预测：指定参数预测RT值
- 批量预测：处理大量数据
- 智能预测：结合插值和模型预测
- 置信度评估：Monte Carlo Dropout

### 4. 数据补全
- 补全完全缺失的组合
- 补全部分缺失的qty范围
- 智能质量控制

### 5. 可视化分析
- 数据分布分析
- 模型性能评估
- 预测结果可视化
- 交互式仪表板

## 📦 安装说明

### 环境要求
- Python 3.8+
- TensorFlow 2.10+
- 其他依赖见 `requirements.txt`

### 安装步骤

```bash
# 克隆项目
git clone <repository-url>
cd semiconductor-rt-prediction

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

## 🔧 快速开始

### 1. 基础使用

```python
from data_processor import EquipmentDataProcessor
from training_pipeline import TrainingPipeline
from prediction_engine import RTPredictor
import pandas as pd

# 加载数据
df = pd.read_csv('your_data.csv')

# 数据预处理
processor = EquipmentDataProcessor()
pipeline = TrainingPipeline(processor)

# 准备数据
X_train, X_val, X_test, y_train, y_val, y_test, _ = pipeline.prepare_data(df)

# 训练模型
result = pipeline.train_model(X_train, y_train, X_val, y_val)
model = result['model']

# 创建预测器
predictor = RTPredictor(model, processor)

# 单个预测
prediction = predictor.predict_single(
    capability='CAP_A',
    recipe='RECIPE_1',
    equip='EQP_001', 
    sub_equip='SUB_A',
    qty=15
)
print(f"预测RT: {prediction['predicted_rt']:.2f}")
```

### 2. 运行完整示例

```bash
python main_example.py
```

### 3. 数据补全示例

```python
from prediction_engine import DataCompletion

# 创建数据补全器
completion = DataCompletion(predictor, qty_range=(1, 25))

# 补全缺失组合
completed_data = completion.complete_partial_combinations(df, min_coverage=0.8)
```

## 📊 数据格式

输入数据应包含以下列：

| 列名 | 类型 | 描述 | 示例 |
|------|------|------|------|
| capability | string | 机台能力类型 | CAP_A, CAP_B |
| recipe | string | 制程菜单 | RECIPE_1, RECIPE_2 |
| equip | string | 机台名称 | EQP_001, EQP_002 |
| sub_equip | string | 子机台 | SUB_A, SUB_B |
| qty | int | WIP片数 | 1-25 |
| rt | float | 运行时间（目标） | 120.5, 98.2 |

## ⚙️ 配置说明

系统使用 `config.yaml` 进行配置，主要配置项：

```yaml
# 模型配置
model:
  type: "standard"  # standard, transformer, wide_deep
  architecture:
    hidden_units: [256, 128, 64, 32]
    dropout_rate: 0.3
    l2_reg: 0.001

# 训练配置  
training:
  epochs: 100
  batch_size: 32
  early_stopping:
    patience: 20
```

## 🎨 可视化功能

### 数据分析
```python
from visualization_tools import DataAnalyzer, Visualizer

analyzer = DataAnalyzer(df)
visualizer = Visualizer(df)

# 基础统计
stats = analyzer.basic_statistics()

# 绘制分布图
visualizer.plot_rt_distribution()

# 相关性分析
visualizer.plot_correlation_matrix()
```

### 模型性能分析
```python
from visualization_tools import ModelPerformanceAnalyzer

perf_analyzer = ModelPerformanceAnalyzer()

# 训练历史分析
perf_analyzer.analyze_training_history(history.history)

# 预测结果分析
visualizer.plot_prediction_results(y_true, y_pred)
```

## 🔍 高级功能

### 1. 超参数优化
```python
# 使用Optuna进行超参数搜索
optimization_result = pipeline.hyperparameter_optimization(
    X_train, y_train, X_val, y_val, n_trials=50
)
```

### 2. 交叉验证
```python
# K折交叉验证
cv_scores, cv_summary = pipeline.cross_validate(X, y, cv_folds=5)
```

### 3. 模型集成
```python
from model_architecture import AdvancedEquipmentPredictor

# 创建集成模型
predictor = AdvancedEquipmentPredictor(input_dim=X.shape[1])
ensemble_models = predictor.build_ensemble_model(num_models=3)
```

## 📈 性能指标

系统使用以下指标评估模型性能：

- **MAE**: 平均绝对误差
- **MSE**: 均方误差  
- **RMSE**: 均方根误差
- **R²**: 决定系数
- **MAPE**: 平均绝对百分比误差

## 🛠️ 项目结构

```
├── data_processor.py          # 数据预处理模块
├── model_architecture.py      # 模型架构定义
├── training_pipeline.py       # 训练管道
├── prediction_engine.py       # 预测引擎
├── visualization_tools.py     # 可视化工具
├── main_example.py           # 使用示例
├── config.yaml              # 配置文件
├── requirements.txt         # 依赖包
└── README.md               # 项目文档
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📝 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件至：[<EMAIL>]

## 🙏 致谢

感谢半导体制造领域的专家提供的宝贵建议和数据支持。
