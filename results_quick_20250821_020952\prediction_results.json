{"timestamp": "20250821_020952", "mode": "quick", "model_metrics": {"mae": 132.68417516732038, "mse": 26100.77702878485, "rmse": 161.55734903985288, "r2": -14.685625280241661, "mape": 84.31700317913472}, "single_prediction": {"predicted_rt": 77.68968200683594, "confidence": 0.999863612966938, "input_features": {"capability": "CAP_A", "recipe": "RECIPE_1", "equip": "EQP_001", "sub_equip": "SUB_A", "qty": 15}}, "smart_prediction": {"predicted_rt": 184514.21875, "confidence": 0.9998817959171158, "input_features": {"capability": "CAP_B", "recipe": "RECIPE_2", "equip": "EQP_002", "sub_equip": "SUB_B", "qty": 12}, "method": "direct"}, "training_config": {"model_params": {"hidden_units": [32, 16], "dropout_rate": 0.1, "l2_reg": 0.01}, "training_params": {"epochs": 5, "batch_size": 64, "learning_rate": 0.02, "patience": 3, "verbose": 1}}, "data_info": {"total_samples": 140, "missing_samples": 60, "completed_samples": 200}}